#!/usr/bin/env python3
"""
测试Times New Roman字体和文字旋转功能
"""

from PIL import Image, ImageDraw, ImageFont

def test_font_and_rotation():
    print("测试Times New Roman字体和文字旋转...")
    
    # 创建测试画布
    canvas = Image.new('RGB', (400, 300), 'white')
    draw = ImageDraw.Draw(canvas)
    
    # 尝试加载Times New Roman字体
    font_loaded = False
    font_name = "默认字体"
    
    try:
        font = ImageFont.truetype("times.ttf", 18)
        font_name = "times.ttf"
        font_loaded = True
        print("✅ 成功加载 times.ttf")
    except:
        try:
            font = ImageFont.truetype("C:/Windows/Fonts/times.ttf", 18)
            font_name = "C:/Windows/Fonts/times.ttf"
            font_loaded = True
            print("✅ 成功加载 C:/Windows/Fonts/times.ttf")
        except:
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/timesbd.ttf", 18)
                font_name = "C:/Windows/Fonts/timesbd.ttf"
                font_loaded = True
                print("✅ 成功加载 C:/Windows/Fonts/timesbd.ttf")
            except:
                try:
                    font = ImageFont.truetype("arial.ttf", 18)
                    font_name = "arial.ttf (备用)"
                    font_loaded = True
                    print("⚠️ 使用备用字体 arial.ttf")
                except:
                    font = ImageFont.load_default()
                    font_name = "系统默认字体"
                    print("⚠️ 使用系统默认字体")
    
    # 测试文字旋转
    test_texts = ["DeepCrack", "GAPS384", "CrackLS315"]
    
    for i, text in enumerate(test_texts):
        try:
            # 创建临时图片来绘制旋转的文字
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            # 创建临时画布
            temp_img = Image.new('RGBA', (text_width + 10, text_height + 10), (255, 255, 255, 0))
            temp_draw = ImageDraw.Draw(temp_img)
            temp_draw.text((5, 5), text, fill='black', font=font)
            
            # 旋转文字90度
            rotated_text = temp_img.rotate(90, expand=True)
            
            # 放置到主画布
            x = 50 + i * 100
            y = 50
            canvas.paste(rotated_text, (x, y), rotated_text)
            
            print(f"✅ 成功旋转文字: {text}")
            
        except Exception as e:
            print(f"❌ 旋转文字失败 {text}: {e}")
    
    # 添加说明文字
    draw.text((50, 250), f"字体: {font_name}", fill='black', font=font)
    
    # 保存测试图片
    canvas.save('test_font_rotation.png')
    print(f"\n✅ 测试图片已保存: test_font_rotation.png")
    print(f"使用字体: {font_name}")

if __name__ == "__main__":
    test_font_and_rotation()
