#!/usr/bin/env python3
"""
脚本用于从三个数据集中随机选择示例图片和标签，用于论文展示
数据集: DeepCrack, GAPS384, CrackLS315
每个数据集随机选择5张图片及其对应的标签
"""

import os
import random
import shutil
from pathlib import Path
import argparse

def get_image_files(dataset_path, subset='TrainDataset'):
    """
    获取指定数据集子集中的所有图片文件
    
    Args:
        dataset_path (str): 数据集路径
        subset (str): 子集名称 (TrainDataset, TestDataset, ValidaDataset)
    
    Returns:
        list: 图片文件名列表
    """
    images_dir = Path(dataset_path) / subset / 'images'
    if not images_dir.exists():
        print(f"警告: 路径不存在 {images_dir}")
        return []
    
    # 支持常见的图片格式
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_files = []
    
    for file_path in images_dir.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            image_files.append(file_path.name)
    
    return image_files

def copy_files(src_dataset, dst_dir, image_files, subset='TrainDataset'):
    """
    复制选中的图片和对应的标签到目标目录
    
    Args:
        src_dataset (str): 源数据集路径
        dst_dir (str): 目标目录
        image_files (list): 要复制的图片文件名列表
        subset (str): 子集名称
    """
    src_images_dir = Path(src_dataset) / subset / 'images'
    src_masks_dir = Path(src_dataset) / subset / 'masks'
    
    dst_images_dir = Path(dst_dir) / 'images'
    dst_masks_dir = Path(dst_dir) / 'masks'
    
    # 创建目标目录
    dst_images_dir.mkdir(parents=True, exist_ok=True)
    dst_masks_dir.mkdir(parents=True, exist_ok=True)
    
    for image_file in image_files:
        # 复制图片
        src_image_path = src_images_dir / image_file
        dst_image_path = dst_images_dir / image_file
        
        if src_image_path.exists():
            shutil.copy2(src_image_path, dst_image_path)
            print(f"复制图片: {src_image_path} -> {dst_image_path}")
        else:
            print(f"警告: 图片文件不存在 {src_image_path}")
            continue
        
        # 查找对应的标签文件
        # 尝试不同的标签文件扩展名
        image_stem = Path(image_file).stem
        mask_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif']
        
        mask_copied = False
        for ext in mask_extensions:
            mask_file = image_stem + ext
            src_mask_path = src_masks_dir / mask_file
            
            if src_mask_path.exists():
                dst_mask_path = dst_masks_dir / mask_file
                shutil.copy2(src_mask_path, dst_mask_path)
                print(f"复制标签: {src_mask_path} -> {dst_mask_path}")
                mask_copied = True
                break
        
        if not mask_copied:
            print(f"警告: 找不到图片 {image_file} 对应的标签文件")

def main():
    parser = argparse.ArgumentParser(description='从数据集中随机选择示例用于论文展示')
    parser.add_argument('--output_dir', '-o', default='paper_examples', 
                       help='输出目录 (默认: paper_examples)')
    parser.add_argument('--num_samples', '-n', type=int, default=5,
                       help='每个数据集选择的样本数量 (默认: 5)')
    parser.add_argument('--seed', '-s', type=int, default=42,
                       help='随机种子 (默认: 42)')
    parser.add_argument('--subset', default='TrainDataset',
                       choices=['TrainDataset', 'TestDataset', 'ValidaDataset'],
                       help='使用的数据集子集 (默认: TrainDataset)')
    
    args = parser.parse_args()
    
    # 设置随机种子以确保结果可重现
    random.seed(args.seed)
    
    # 定义三个数据集
    datasets = {
        'DeepCrack': 'DeepCrack',
        'GAPS384': 'GAPS384', 
        'CrackLS315': 'CrackLS315'
    }
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    print(f"开始从三个数据集中选择示例...")
    print(f"输出目录: {output_dir.absolute()}")
    print(f"每个数据集选择样本数: {args.num_samples}")
    print(f"使用子集: {args.subset}")
    print(f"随机种子: {args.seed}")
    print("-" * 50)
    
    total_selected = 0
    
    for dataset_name, dataset_path in datasets.items():
        print(f"\n处理数据集: {dataset_name}")
        
        # 检查数据集是否存在
        if not Path(dataset_path).exists():
            print(f"错误: 数据集路径不存在 {dataset_path}")
            continue
        
        # 获取所有图片文件
        image_files = get_image_files(dataset_path, args.subset)
        
        if not image_files:
            print(f"警告: 在 {dataset_path}/{args.subset}/images 中没有找到图片文件")
            continue
        
        print(f"找到 {len(image_files)} 张图片")
        
        # 随机选择指定数量的图片
        num_to_select = min(args.num_samples, len(image_files))
        selected_images = random.sample(image_files, num_to_select)
        
        print(f"随机选择了 {num_to_select} 张图片:")
        for img in selected_images:
            print(f"  - {img}")
        
        # 创建数据集特定的输出目录
        dataset_output_dir = output_dir / dataset_name
        
        # 复制选中的文件
        copy_files(dataset_path, dataset_output_dir, selected_images, args.subset)
        
        total_selected += num_to_select
        print(f"完成 {dataset_name} 的处理")
    
    print("-" * 50)
    print(f"总共选择了 {total_selected} 个样本")
    print(f"结果保存在: {output_dir.absolute()}")
    
    # 生成摘要文件
    summary_file = output_dir / 'selection_summary.txt'
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(f"论文示例数据集选择摘要\n")
        f.write(f"生成时间: {Path(__file__).stat().st_mtime}\n")
        f.write(f"随机种子: {args.seed}\n")
        f.write(f"使用子集: {args.subset}\n")
        f.write(f"每个数据集样本数: {args.num_samples}\n")
        f.write(f"总样本数: {total_selected}\n")
        f.write(f"\n数据集:\n")
        for dataset_name in datasets.keys():
            f.write(f"- {dataset_name}\n")
    
    print(f"选择摘要保存在: {summary_file}")

if __name__ == "__main__":
    main()
