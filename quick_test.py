#!/usr/bin/env python3
"""
快速测试脚本 - 生成简化版本的网格图片
"""

import random
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path

def main():
    print("开始快速测试...")
    
    # 检查PIL
    try:
        test_img = Image.new('RGB', (100, 100), 'white')
        print("✅ PIL 可用")
    except Exception as e:
        print(f"❌ PIL 错误: {e}")
        return
    
    # 检查数据集
    datasets = ['DeepCrack', 'GAPS384', 'CrackLS315']
    for dataset in datasets:
        dataset_path = Path(dataset)
        if dataset_path.exists():
            images_dir = dataset_path / 'TrainDataset' / 'images'
            if images_dir.exists():
                image_files = [f for f in images_dir.iterdir() 
                             if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png']]
                print(f"✅ {dataset}: {len(image_files)} 张图片")
            else:
                print(f"❌ {dataset}: images 目录不存在")
        else:
            print(f"❌ {dataset}: 数据集不存在")
    
    # 尝试生成一个简单的测试图片
    try:
        canvas = Image.new('RGB', (800, 600), 'white')
        draw = ImageDraw.Draw(canvas)
        
        # 尝试加载字体
        try:
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            font = ImageFont.load_default()
        
        draw.text((50, 50), "测试图片生成成功", fill='black', font=font)
        canvas.save('test_simple.png')
        print("✅ 测试图片生成成功: test_simple.png")
        
    except Exception as e:
        print(f"❌ 图片生成失败: {e}")

if __name__ == "__main__":
    main()
