# 论文数据集示例图片生成器 - 使用指南

## 概述

这个工具可以帮您从三个裂缝分割数据集（DeepCrack、GAPS384、CrackLS315）中随机选择样本，生成类似您提供示例的论文图片，将原图和标签（Ground Truth）并排显示。

## 快速开始

### 1. 安装依赖库
双击运行 `install_dependencies.bat` 或在命令行中运行：
```bash
pip install Pillow numpy
```

### 2. 测试功能
双击运行 `run_test.bat` 或在命令行中运行：
```bash
python simple_test.py
```

### 3. 生成论文图片
双击运行 `run_generate.bat` 或在命令行中运行：
```bash
python generate_paper_figure.py
```

## 文件说明

### 主要脚本
- `generate_paper_figure.py` - 主要的图片生成脚本
- `simple_test.py` - 测试脚本，检查环境和数据集
- `generate_paper_examples.py` - 原始的文件复制脚本（备用）

### 批处理文件（Windows）
- `install_dependencies.bat` - 安装依赖库
- `run_test.bat` - 运行测试
- `run_generate.bat` - 生成示例图片

### 说明文档
- `README_figure_generation.md` - 详细的技术文档
- `使用指南.md` - 本文件，简化的使用说明

## 生成的图片格式

脚本会生成一张包含所有三个数据集示例的大图片，格式如下：

```
┌─────────────────────────────────────┐
│           DeepCrack Dataset         │
├─────────────┬───────────────────────┤
│ Original    │ Ground Truth          │
│ [裂缝图1]   │ [标签图1]            │
├─────────────┼───────────────────────┤
│ Original    │ Ground Truth          │
│ [裂缝图2]   │ [标签图2]            │
└─────────────┴───────────────────────┘

┌─────────────────────────────────────┐
│           GAPS384 Dataset           │
├─────────────┬───────────────────────┤
│ Original    │ Ground Truth          │
│ [裂缝图1]   │ [标签图1]            │
└─────────────┴───────────────────────┘

┌─────────────────────────────────────┐
│          CrackLS315 Dataset         │
├─────────────┬───────────────────────┤
│ Original    │ Ground Truth          │
│ [裂缝图1]   │ [标签图1]            │
└─────────────┴───────────────────────┘
```

## 自定义参数

如果需要自定义参数，可以直接修改 `run_generate.bat` 文件，或在命令行中使用：

```bash
# 每个数据集选择3个样本
python generate_paper_figure.py --num_samples 3

# 自定义输出文件名
python generate_paper_figure.py --output my_paper_figure.png

# 使用测试数据集而不是训练数据集
python generate_paper_figure.py --subset TestDataset

# 设置随机种子确保结果可重现
python generate_paper_figure.py --seed 2024
```

## 常见问题

### Q: 运行时提示缺少依赖库
A: 运行 `install_dependencies.bat` 安装所需的库

### Q: 提示找不到数据集
A: 确保 DeepCrack、GAPS384、CrackLS315 这三个文件夹存在于当前目录

### Q: 生成的图片太大或太小
A: 可以调整 `--num_samples` 参数来控制每个数据集的样本数量

### Q: 想要不同的图片布局
A: 可以修改 `generate_paper_figure.py` 中的布局参数

## 输出文件

默认情况下，脚本会生成：
- `dataset_examples.png` - 包含所有数据集示例的综合图片

## 技术细节

- 图片会被自动调整为 256x256 像素
- 支持 jpg, png, bmp, tiff 等常见格式
- 自动匹配图片和对应的标签文件
- 使用随机种子确保结果可重现

## 联系支持

如果遇到问题，请检查：
1. Python 环境是否正确安装
2. 依赖库是否已安装
3. 数据集目录结构是否正确
4. 图片和标签文件是否存在且可读取
