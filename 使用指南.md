# 论文数据集示例图片生成器 - 使用指南

## 概述

这个工具可以帮您从三个裂缝分割数据集（DeepCrack、GAPS384、CrackLS315）中随机选择样本，生成类似您提供示例的论文图片，将原图和标签（Ground Truth）并排显示，每个数据集选择5张图片。

## 快速开始

### 1. 安装依赖库
双击运行 `install_dependencies.bat` 或在命令行中运行：
```bash
pip install Pillow
```

### 2. 测试功能
运行测试脚本检查环境：
```bash
python test_grid.py
```

### 3. 生成论文图片
双击运行 `run_generate.bat` 或在命令行中运行：
```bash
python generate_paper_grid.py
```

## 文件说明

### 主要脚本
- `generate_paper_grid.py` - 主要的网格图片生成脚本（推荐使用）
- `test_grid.py` - 简单测试脚本，检查环境和数据集
- `generate_paper_figure.py` - 备用的图片生成脚本
- `generate_paper_examples.py` - 文件复制脚本（备用）

### 批处理文件（Windows）
- `install_dependencies.bat` - 安装依赖库
- `run_generate.bat` - 生成示例图片（使用网格格式）

### 说明文档
- `README_figure_generation.md` - 详细的技术文档
- `使用指南.md` - 本文件，简化的使用说明

## 生成的图片格式

脚本会生成一张包含所有三个数据集示例的网格图片，每个数据集5张图片，格式类似您提供的示例：

```
                    Dataset Examples

                   DeepCrack Dataset
┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐
│Original │Gr.Truth │Original │Gr.Truth │Original │Gr.Truth │Original │Gr.Truth │Original │Gr.Truth │
│[图片1]  │[标签1]  │[图片2]  │[标签2]  │[图片3]  │[标签3]  │[图片4]  │[标签4]  │[图片5]  │[标签5]  │
└─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘

                   GAPS384 Dataset
┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐
│Original │Gr.Truth │Original │Gr.Truth │Original │Gr.Truth │Original │Gr.Truth │Original │Gr.Truth │
│[图片1]  │[标签1]  │[图片2]  │[标签2]  │[图片3]  │[标签3]  │[图片4]  │[标签4]  │[图片5]  │[标签5]  │
└─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘

                  CrackLS315 Dataset
┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐
│Original │Gr.Truth │Original │Gr.Truth │Original │Gr.Truth │Original │Gr.Truth │Original │Gr.Truth │
│[图片1]  │[标签1]  │[图片2]  │[标签2]  │[图片3]  │[标签3]  │[图片4]  │[标签4]  │[图片5]  │[标签5]  │
└─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘
```

## 自定义参数

如果需要自定义参数，可以直接修改 `run_generate.bat` 文件，或在命令行中使用：

```bash
# 默认生成（每个数据集5个样本）
python generate_paper_grid.py

# 自定义输出文件名
python generate_paper_grid.py --output my_paper_figure.png

# 使用测试数据集而不是训练数据集
python generate_paper_grid.py --subset TestDataset

# 设置随机种子确保结果可重现
python generate_paper_grid.py --seed 2024

# 每个数据集选择3个样本（不推荐，因为您要求5个）
python generate_paper_grid.py --num_samples 3
```

## 常见问题

### Q: 运行时提示缺少依赖库
A: 运行 `install_dependencies.bat` 安装所需的库

### Q: 提示找不到数据集
A: 确保 DeepCrack、GAPS384、CrackLS315 这三个文件夹存在于当前目录

### Q: 生成的图片太大或太小
A: 可以调整 `--num_samples` 参数来控制每个数据集的样本数量

### Q: 想要不同的图片布局
A: 可以修改 `generate_paper_figure.py` 中的布局参数

## 输出文件

默认情况下，脚本会生成：
- `dataset_grid.png` - 包含所有数据集示例的网格图片（每个数据集5张图片）

## 技术细节

- 图片会被自动调整为 200x200 像素
- 支持 jpg, png, bmp, tiff 等常见格式
- 自动匹配图片和对应的标签文件
- 使用随机种子确保结果可重现
- 每个数据集固定选择5张图片
- 生成网格布局，每行显示5个样本对（原图+标签）

## 联系支持

如果遇到问题，请检查：
1. Python 环境是否正确安装
2. 依赖库是否已安装
3. 数据集目录结构是否正确
4. 图片和标签文件是否存在且可读取
