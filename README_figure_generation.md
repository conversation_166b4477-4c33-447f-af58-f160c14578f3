# 论文数据集示例图片生成器

这个脚本用于生成论文中展示数据集的示例图片，将原图和对应的标签（Ground Truth）并排显示，类似您提供的示例图片格式。

## 功能特点

- 从三个数据集（DeepCrack、GAPS384、CrackLS315）中随机选择样本
- 生成原图和标签的并排对比图
- 自动调整图片大小为统一尺寸（256x256）
- 为每个数据集创建网格布局
- 生成包含所有数据集的综合图片
- 添加数据集标题和图片标签

## 依赖库

```bash
pip install Pillow numpy
```

## 使用方法

### 基本使用
```bash
python generate_paper_figure.py
```

这将生成一个名为 `dataset_examples.png` 的图片文件，包含三个数据集各5个样本的对比图。

### 自定义参数
```bash
python generate_paper_figure.py --output my_figure.png --num_samples 3 --seed 123
```

### 参数说明

- `--output, -o`: 输出图片文件名（默认：dataset_examples.png）
- `--num_samples, -n`: 每个数据集选择的样本数量（默认：5）
- `--seed, -s`: 随机种子，确保结果可重现（默认：42）
- `--subset`: 使用的数据集子集（默认：TrainDataset）
  - 可选值：TrainDataset, TestDataset, ValidaDataset

## 输出格式

生成的图片将包含：

1. **每个样本**: 原图和标签并排显示，下方标注 "Original" 和 "Ground Truth"
2. **数据集网格**: 每个数据集的样本排列成网格，顶部显示数据集名称
3. **综合图片**: 所有数据集垂直排列在一张图片中

## 图片布局示例

```
┌─────────────────────────────────────┐
│           DeepCrack Dataset         │
├─────────────┬───────────────────────┤
│ Original    │ Ground Truth          │
│ [Image 1]   │ [Mask 1]             │
├─────────────┼───────────────────────┤
│ Original    │ Ground Truth          │
│ [Image 2]   │ [Mask 2]             │
└─────────────┴───────────────────────┘

┌─────────────────────────────────────┐
│           GAPS384 Dataset           │
├─────────────┬───────────────────────┤
│ Original    │ Ground Truth          │
│ [Image 1]   │ [Mask 1]             │
└─────────────┴───────────────────────┘

... (其他数据集)
```

## 注意事项

1. 确保数据集目录存在且包含正确的子目录结构
2. 脚本会自动匹配图片和标签文件（基于文件名）
3. 图片会被调整为256x256像素以保持一致性
4. 支持多种图片格式（jpg, png, bmp, tiff等）
5. 如果找不到对应的标签文件，该样本会被跳过

## 故障排除

### 字体问题
如果遇到字体相关错误，脚本会自动降级使用默认字体。

### 内存问题
如果处理大量图片时遇到内存问题，可以减少 `--num_samples` 参数的值。

### 图片格式问题
确保图片文件格式正确，脚本支持常见的图片格式。

## 示例命令

```bash
# 生成默认图片（每个数据集5个样本）
python generate_paper_figure.py

# 生成较少样本的图片
python generate_paper_figure.py --num_samples 3 --output small_examples.png

# 使用测试数据集
python generate_paper_figure.py --subset TestDataset --output test_examples.png

# 使用特定随机种子
python generate_paper_figure.py --seed 2024 --output reproducible_examples.png
```
