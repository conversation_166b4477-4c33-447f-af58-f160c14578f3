#!/usr/bin/env python3
"""
测试网格生成器的简化版本
"""

from pathlib import Path

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试论文网格生成器 ===\n")
    
    # 检查依赖
    try:
        from PIL import Image, ImageDraw, ImageFont
        print("✅ PIL (Pillow) 可用")
    except ImportError:
        print("❌ PIL (Pillow) 未安装")
        return False
    
    # 检查数据集
    datasets = ['DeepCrack', 'GAPS384', 'CrackLS315']
    available = []
    
    for dataset in datasets:
        dataset_path = Path(dataset)
        if dataset_path.exists():
            train_images = dataset_path / 'TrainDataset' / 'images'
            train_masks = dataset_path / 'TrainDataset' / 'masks'
            
            if train_images.exists() and train_masks.exists():
                image_count = len([f for f in train_images.iterdir() 
                                 if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png']])
                if image_count > 0:
                    print(f"✅ {dataset}: {image_count} 张图片")
                    available.append(dataset)
                else:
                    print(f"❌ {dataset}: 没有图片文件")
            else:
                print(f"❌ {dataset}: 目录结构不完整")
        else:
            print(f"❌ {dataset}: 路径不存在")
    
    if not available:
        print("\n❌ 没有可用的数据集")
        return False
    
    print(f"\n✅ 找到 {len(available)} 个可用数据集")
    print("可以运行: python generate_paper_grid.py")
    
    return True

if __name__ == "__main__":
    test_basic_functionality()
