#!/usr/bin/env python3
"""
简单测试脚本 - 检查依赖和基本功能
"""

import sys
from pathlib import Path

def check_dependencies():
    """检查依赖库"""
    print("检查依赖库...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        print("✅ PIL (Pillow) 可用")
    except ImportError:
        print("❌ PIL (Pillow) 未安装，请运行: pip install Pillow")
        return False
    
    try:
        import numpy as np
        print("✅ NumPy 可用")
    except ImportError:
        print("❌ NumPy 未安装，请运行: pip install numpy")
        return False
    
    return True

def check_datasets():
    """检查数据集"""
    print("\n检查数据集...")
    
    datasets = ['DeepCrack', 'GAPS384', 'CrackLS315']
    available_datasets = []
    
    for dataset in datasets:
        dataset_path = Path(dataset)
        if dataset_path.exists():
            train_images = dataset_path / 'TrainDataset' / 'images'
            train_masks = dataset_path / 'TrainDataset' / 'masks'
            
            if train_images.exists() and train_masks.exists():
                image_count = len([f for f in train_images.iterdir() 
                                 if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png']])
                print(f"✅ {dataset}: {image_count} 张图片")
                available_datasets.append(dataset)
            else:
                print(f"❌ {dataset}: 目录结构不完整")
        else:
            print(f"❌ {dataset}: 路径不存在")
    
    return available_datasets

def test_image_processing():
    """测试图片处理功能"""
    print("\n测试图片处理...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建测试图片
        test_img = Image.new('RGB', (100, 100), 'red')
        test_mask = Image.new('L', (100, 100), 128)
        
        # 测试调整大小
        resized_img = test_img.resize((50, 50))
        resized_mask = test_mask.resize((50, 50))
        
        # 测试创建组合图片
        combined = Image.new('RGB', (110, 50), 'white')
        combined.paste(resized_img, (0, 0))
        combined.paste(resized_mask.convert('RGB'), (60, 0))
        
        # 测试保存
        combined.save('test_output.png')
        print("✅ 图片处理功能正常")
        
        # 清理测试文件
        Path('test_output.png').unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        print(f"❌ 图片处理测试失败: {e}")
        return False

def main():
    print("=== 论文图片生成器测试 ===")
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先安装缺失的依赖库")
        return
    
    # 检查数据集
    available_datasets = check_datasets()
    if not available_datasets:
        print("\n没有找到可用的数据集")
        return
    
    # 测试图片处理
    if not test_image_processing():
        print("\n图片处理功能测试失败")
        return
    
    print(f"\n🎉 所有测试通过！")
    print(f"可用数据集: {', '.join(available_datasets)}")
    print(f"可以运行主脚本: python generate_paper_figure.py")

if __name__ == "__main__":
    main()
