#!/usr/bin/env python3
"""
生成论文中的数据集示例图片
从三个数据集中随机选择图片，生成原图和标签的对比图
"""

import os
import random
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path
import argparse

def get_image_files(dataset_path, subset='TrainDataset'):
    """获取指定数据集子集中的所有图片文件"""
    images_dir = Path(dataset_path) / subset / 'images'
    if not images_dir.exists():
        print(f"警告: 路径不存在 {images_dir}")
        return []
    
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_files = []
    
    for file_path in images_dir.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            image_files.append(file_path.name)
    
    return image_files

def find_mask_file(image_file, masks_dir):
    """查找对应的标签文件"""
    image_stem = Path(image_file).stem
    mask_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif']
    
    for ext in mask_extensions:
        mask_file = masks_dir / (image_stem + ext)
        if mask_file.exists():
            return mask_file
    return None

def resize_image(image, target_size=(256, 256)):
    """调整图片大小，保持宽高比"""
    return image.resize(target_size, Image.Resampling.LANCZOS)

def create_comparison_image(image_path, mask_path, dataset_name):
    """创建原图和标签的对比图"""
    try:
        # 加载图片
        original = Image.open(image_path).convert('RGB')
        mask = Image.open(mask_path).convert('L')  # 转为灰度图
        
        # 调整大小
        target_size = (256, 256)
        original_resized = resize_image(original, target_size)
        mask_resized = resize_image(mask, target_size)
        
        # 创建并排图片 (原图 | 标签)
        combined_width = target_size[0] * 2 + 10  # 中间留10像素间隔
        combined_height = target_size[1] + 40  # 底部留40像素放文字
        
        combined = Image.new('RGB', (combined_width, combined_height), 'white')
        
        # 粘贴原图和标签
        combined.paste(original_resized, (0, 0))
        combined.paste(mask_resized.convert('RGB'), (target_size[0] + 10, 0))
        
        # 添加文字标签
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 16)  # 中文字体
            except:
                font = ImageFont.load_default()
        
        draw = ImageDraw.Draw(combined)
        
        # 添加 "Original" 和 "Ground Truth" 标签
        draw.text((target_size[0]//2 - 30, target_size[1] + 10), "Original", 
                 fill='black', font=font)
        draw.text((target_size[0] + target_size[0]//2 - 40, target_size[1] + 10), "Ground Truth", 
                 fill='black', font=font)
        
        return combined
        
    except Exception as e:
        print(f"处理图片时出错 {image_path}: {e}")
        return None

def create_dataset_grid(dataset_name, dataset_path, num_samples=5, subset='TrainDataset'):
    """为单个数据集创建网格图片"""
    print(f"\n处理数据集: {dataset_name}")
    
    # 获取图片文件
    image_files = get_image_files(dataset_path, subset)
    if not image_files:
        print(f"在 {dataset_path}/{subset}/images 中没有找到图片文件")
        return None
    
    print(f"找到 {len(image_files)} 张图片")
    
    # 随机选择图片
    num_to_select = min(num_samples, len(image_files))
    selected_images = random.sample(image_files, num_to_select)
    print(f"随机选择了 {num_to_select} 张图片")
    
    # 创建对比图片
    comparison_images = []
    images_dir = Path(dataset_path) / subset / 'images'
    masks_dir = Path(dataset_path) / subset / 'masks'
    
    for img_file in selected_images:
        image_path = images_dir / img_file
        mask_path = find_mask_file(img_file, masks_dir)
        
        if mask_path:
            comparison = create_comparison_image(image_path, mask_path, dataset_name)
            if comparison:
                comparison_images.append(comparison)
                print(f"  ✅ 处理完成: {img_file}")
            else:
                print(f"  ❌ 处理失败: {img_file}")
        else:
            print(f"  ❌ 找不到标签: {img_file}")
    
    if not comparison_images:
        print(f"没有成功处理任何图片")
        return None
    
    # 创建网格布局
    single_width = comparison_images[0].width
    single_height = comparison_images[0].height
    
    # 计算网格尺寸 (尽量接近正方形)
    cols = int(np.ceil(np.sqrt(len(comparison_images))))
    rows = int(np.ceil(len(comparison_images) / cols))
    
    # 创建网格图片
    grid_width = single_width * cols + 20 * (cols - 1)  # 列间距20像素
    grid_height = single_height * rows + 20 * (rows - 1) + 60  # 行间距20像素，顶部60像素放标题
    
    grid = Image.new('RGB', (grid_width, grid_height), 'white')
    
    # 添加数据集标题
    try:
        title_font = ImageFont.truetype("arial.ttf", 24)
    except:
        try:
            title_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 24)
        except:
            title_font = ImageFont.load_default()
    
    draw = ImageDraw.Draw(grid)
    title_text = f"{dataset_name} Dataset Examples"
    title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    draw.text(((grid_width - title_width) // 2, 20), title_text, fill='black', font=title_font)
    
    # 放置图片
    for i, img in enumerate(comparison_images):
        row = i // cols
        col = i % cols
        
        x = col * (single_width + 20)
        y = 60 + row * (single_height + 20)  # 60像素是标题区域
        
        grid.paste(img, (x, y))
    
    return grid

def create_combined_figure(datasets, output_path, num_samples=5, subset='TrainDataset'):
    """创建包含所有数据集的综合图片"""
    dataset_grids = []
    
    for dataset_name, dataset_path in datasets.items():
        if not Path(dataset_path).exists():
            print(f"跳过不存在的数据集: {dataset_path}")
            continue
            
        grid = create_dataset_grid(dataset_name, dataset_path, num_samples, subset)
        if grid:
            dataset_grids.append((dataset_name, grid))
    
    if not dataset_grids:
        print("没有成功处理任何数据集")
        return False
    
    # 计算总图片尺寸
    max_width = max(grid.width for _, grid in dataset_grids)
    total_height = sum(grid.height for _, grid in dataset_grids) + 40 * (len(dataset_grids) - 1)
    
    # 创建最终图片
    final_image = Image.new('RGB', (max_width, total_height), 'white')
    
    # 垂直排列数据集
    y_offset = 0
    for dataset_name, grid in dataset_grids:
        # 居中放置
        x_offset = (max_width - grid.width) // 2
        final_image.paste(grid, (x_offset, y_offset))
        y_offset += grid.height + 40
    
    # 保存图片
    final_image.save(output_path, 'PNG', quality=95)
    print(f"\n✅ 综合图片已保存: {output_path}")
    print(f"图片尺寸: {final_image.width} x {final_image.height}")
    
    return True

def main():
    parser = argparse.ArgumentParser(description='生成论文数据集示例图片')
    parser.add_argument('--output', '-o', default='dataset_examples.png', 
                       help='输出图片文件名 (默认: dataset_examples.png)')
    parser.add_argument('--num_samples', '-n', type=int, default=5,
                       help='每个数据集选择的样本数量 (默认: 5)')
    parser.add_argument('--seed', '-s', type=int, default=42,
                       help='随机种子 (默认: 42)')
    parser.add_argument('--subset', default='TrainDataset',
                       choices=['TrainDataset', 'TestDataset', 'ValidaDataset'],
                       help='使用的数据集子集 (默认: TrainDataset)')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(args.seed)
    np.random.seed(args.seed)
    
    # 定义数据集
    datasets = {
        'DeepCrack': 'DeepCrack',
        'GAPS384': 'GAPS384', 
        'CrackLS315': 'CrackLS315'
    }
    
    print(f"开始生成论文数据集示例图片...")
    print(f"输出文件: {args.output}")
    print(f"每个数据集样本数: {args.num_samples}")
    print(f"使用子集: {args.subset}")
    print(f"随机种子: {args.seed}")
    print("-" * 50)
    
    # 创建综合图片
    success = create_combined_figure(datasets, args.output, args.num_samples, args.subset)
    
    if success:
        print(f"\n🎉 成功生成论文示例图片!")
        print(f"文件位置: {Path(args.output).absolute()}")
    else:
        print(f"\n❌ 生成图片失败")

if __name__ == "__main__":
    main()
