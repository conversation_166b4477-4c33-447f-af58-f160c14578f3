#!/usr/bin/env python3
"""
生成论文中的数据集示例图片
从三个数据集中随机选择图片，生成原图和标签的对比图
"""

import random
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path
import argparse

def get_image_files(dataset_path, subset='TrainDataset'):
    """获取指定数据集子集中的所有图片文件"""
    images_dir = Path(dataset_path) / subset / 'images'
    if not images_dir.exists():
        print(f"警告: 路径不存在 {images_dir}")
        return []
    
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_files = []
    
    for file_path in images_dir.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            image_files.append(file_path.name)
    
    return image_files

def find_mask_file(image_file, masks_dir):
    """查找对应的标签文件"""
    image_stem = Path(image_file).stem
    mask_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif']
    
    for ext in mask_extensions:
        mask_file = masks_dir / (image_stem + ext)
        if mask_file.exists():
            return mask_file
    return None

def resize_image(image, target_size=(256, 256)):
    """调整图片大小，保持宽高比"""
    return image.resize(target_size, Image.Resampling.LANCZOS)



def create_dataset_samples(dataset_name, dataset_path, num_samples=5, subset='TrainDataset'):
    """为单个数据集创建样本图片列表"""
    print(f"\n处理数据集: {dataset_name}")

    # 获取图片文件
    image_files = get_image_files(dataset_path, subset)
    if not image_files:
        print(f"在 {dataset_path}/{subset}/images 中没有找到图片文件")
        return []

    print(f"找到 {len(image_files)} 张图片")

    # 随机选择图片
    num_to_select = min(num_samples, len(image_files))
    selected_images = random.sample(image_files, num_to_select)
    print(f"随机选择了 {num_to_select} 张图片")

    # 创建样本图片
    sample_images = []
    images_dir = Path(dataset_path) / subset / 'images'
    masks_dir = Path(dataset_path) / subset / 'masks'

    for img_file in selected_images:
        image_path = images_dir / img_file
        mask_path = find_mask_file(img_file, masks_dir)

        if mask_path:
            try:
                # 加载并调整图片大小
                original = Image.open(image_path).convert('RGB')
                mask = Image.open(mask_path).convert('L')

                # 调整为正方形
                target_size = (200, 200)
                original_resized = resize_image(original, target_size)
                mask_resized = resize_image(mask, target_size)

                sample_images.append({
                    'original': original_resized,
                    'mask': mask_resized,
                    'filename': img_file
                })
                print(f"  ✅ 处理完成: {img_file}")

            except Exception as e:
                print(f"  ❌ 处理失败 {img_file}: {e}")
        else:
            print(f"  ❌ 找不到标签: {img_file}")

    return sample_images

def create_combined_figure(datasets, output_path, num_samples=5, subset='TrainDataset'):
    """创建包含所有数据集的综合图片，类似您提供的示例格式"""
    all_samples = []
    dataset_names = []

    # 收集所有数据集的样本
    for dataset_name, dataset_path in datasets.items():
        if not Path(dataset_path).exists():
            print(f"跳过不存在的数据集: {dataset_path}")
            continue

        samples = create_dataset_samples(dataset_name, dataset_path, num_samples, subset)
        if samples:
            all_samples.extend(samples)
            dataset_names.extend([dataset_name] * len(samples))

    if not all_samples:
        print("没有成功处理任何数据集")
        return False

    # 创建网格布局 - 每行显示5张图片（原图+标签为一组）
    samples_per_row = 5
    total_samples = len(all_samples)
    rows = (total_samples + samples_per_row - 1) // samples_per_row

    # 计算图片尺寸
    sample_width = 200  # 单张图片宽度
    sample_height = 200  # 单张图片高度
    gap = 10  # 图片间距
    label_height = 30  # 标签高度

    # 每个样本包含原图和标签，所以宽度是 sample_width * 2 + gap
    cell_width = sample_width * 2 + gap
    cell_height = sample_height + label_height

    # 总图片尺寸
    total_width = cell_width * samples_per_row + gap * (samples_per_row - 1)
    total_height = cell_height * rows + gap * (rows - 1) + 60  # 顶部留60像素放总标题

    # 创建最终图片
    final_image = Image.new('RGB', (total_width, total_height), 'white')

    # 添加总标题
    try:
        title_font = ImageFont.truetype("arial.ttf", 28)
        label_font = ImageFont.truetype("arial.ttf", 14)
    except:
        try:
            title_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 28)
            label_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 14)
        except:
            title_font = ImageFont.load_default()
            label_font = ImageFont.load_default()

    draw = ImageDraw.Draw(final_image)
    title_text = "Dataset Examples"
    title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    draw.text(((total_width - title_width) // 2, 20), title_text, fill='black', font=title_font)

    # 放置样本图片
    for i, (sample, dataset_name) in enumerate(zip(all_samples, dataset_names)):
        row = i // samples_per_row
        col = i % samples_per_row

        # 计算位置
        x = col * (cell_width + gap)
        y = 60 + row * (cell_height + gap)

        # 放置原图
        final_image.paste(sample['original'], (x, y))

        # 放置标签图
        final_image.paste(sample['mask'].convert('RGB'), (x + sample_width + gap, y))

        # 添加数据集标签
        label_y = y + sample_height + 5
        draw.text((x, label_y), f"{dataset_name}", fill='black', font=label_font)
        draw.text((x + sample_width + gap, label_y), "Ground Truth", fill='black', font=label_font)

    # 保存图片
    final_image.save(output_path, 'PNG', quality=95)
    print(f"\n✅ 综合图片已保存: {output_path}")
    print(f"图片尺寸: {final_image.width} x {final_image.height}")
    print(f"包含 {total_samples} 个样本，来自 {len(set(dataset_names))} 个数据集")

    return True

def main():
    parser = argparse.ArgumentParser(description='生成论文数据集示例图片')
    parser.add_argument('--output', '-o', default='dataset_examples.png', 
                       help='输出图片文件名 (默认: dataset_examples.png)')
    parser.add_argument('--num_samples', '-n', type=int, default=5,
                       help='每个数据集选择的样本数量 (默认: 5)')
    parser.add_argument('--seed', '-s', type=int, default=42,
                       help='随机种子 (默认: 42)')
    parser.add_argument('--subset', default='TrainDataset',
                       choices=['TrainDataset', 'TestDataset', 'ValidaDataset'],
                       help='使用的数据集子集 (默认: TrainDataset)')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(args.seed)
    np.random.seed(args.seed)
    
    # 定义数据集
    datasets = {
        'DeepCrack': 'DeepCrack',
        'GAPS384': 'GAPS384', 
        'CrackLS315': 'CrackLS315'
    }
    
    print(f"开始生成论文数据集示例图片...")
    print(f"输出文件: {args.output}")
    print(f"每个数据集样本数: {args.num_samples}")
    print(f"使用子集: {args.subset}")
    print(f"随机种子: {args.seed}")
    print("-" * 50)
    
    # 创建综合图片
    success = create_combined_figure(datasets, args.output, args.num_samples, args.subset)
    
    if success:
        print(f"\n🎉 成功生成论文示例图片!")
        print(f"文件位置: {Path(args.output).absolute()}")
    else:
        print(f"\n❌ 生成图片失败")

if __name__ == "__main__":
    main()
