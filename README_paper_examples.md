# 论文示例数据生成脚本

这个脚本用于从三个裂缝分割数据集（DeepCrack、GAPS384、CrackLS315）中随机选择示例图片和对应的标签，用于论文中的数据集展示。

## 功能特点

- 从三个指定数据集中随机选择样本
- 每个数据集默认选择5张图片及其对应的标签
- 支持自定义选择数量和随机种子
- 自动复制图片和对应的标签文件
- 生成选择摘要文件
- 支持多种图片格式（jpg, png, bmp, tiff等）

## 使用方法

### 基本使用
```bash
python generate_paper_examples.py
```

这将：
- 从每个数据集的TrainDataset中随机选择5张图片
- 将结果保存到 `paper_examples/` 目录
- 使用随机种子42确保结果可重现

### 自定义参数
```bash
python generate_paper_examples.py --output_dir my_examples --num_samples 3 --seed 123 --subset TestDataset
```

### 参数说明

- `--output_dir, -o`: 输出目录（默认：paper_examples）
- `--num_samples, -n`: 每个数据集选择的样本数量（默认：5）
- `--seed, -s`: 随机种子（默认：42）
- `--subset`: 使用的数据集子集（默认：TrainDataset）
  - 可选值：TrainDataset, TestDataset, ValidaDataset

### 查看帮助
```bash
python generate_paper_examples.py --help
```

## 输出结构

脚本运行后会创建如下目录结构：

```
paper_examples/
├── DeepCrack/
│   ├── images/
│   │   ├── image1.jpg
│   │   ├── image2.jpg
│   │   └── ...
│   └── masks/
│       ├── image1.png
│       ├── image2.png
│       └── ...
├── GAPS384/
│   ├── images/
│   └── masks/
├── CrackLS315/
│   ├── images/
│   └── masks/
└── selection_summary.txt
```

## 注意事项

1. 确保三个数据集目录存在且包含正确的子目录结构
2. 脚本会自动匹配图片和标签文件（基于文件名）
3. 如果某个数据集的图片数量少于请求的样本数，会选择所有可用的图片
4. 脚本支持不同的图片和标签文件格式

## 数据集结构要求

每个数据集应该有以下结构：
```
DatasetName/
├── TrainDataset/
│   ├── images/
│   └── masks/
├── TestDataset/
│   ├── images/
│   └── masks/
└── ValidaDataset/  (可选)
    ├── images/
    └── masks/
```

## 示例输出

运行脚本时会显示详细的处理信息：
```
开始从三个数据集中选择示例...
输出目录: D:\crack_segmentation\dataset\paper_examples
每个数据集选择样本数: 5
使用子集: TrainDataset
随机种子: 42
--------------------------------------------------

处理数据集: DeepCrack
找到 300 张图片
随机选择了 5 张图片:
  - crack_001.jpg
  - crack_045.jpg
  - crack_123.jpg
  - crack_200.jpg
  - crack_267.jpg
复制图片: DeepCrack\TrainDataset\images\crack_001.jpg -> paper_examples\DeepCrack\images\crack_001.jpg
复制标签: DeepCrack\TrainDataset\masks\crack_001.png -> paper_examples\DeepCrack\masks\crack_001.png
...
```
