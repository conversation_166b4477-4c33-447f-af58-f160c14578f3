#!/usr/bin/env python3
"""
简化测试脚本 - 验证数据集结构和文件匹配
"""

import os
from pathlib import Path

def test_dataset_structure():
    """测试数据集结构"""
    datasets = ['DeepCrack', 'GAPS384', 'CrackLS315']
    
    for dataset in datasets:
        print(f"\n=== 测试数据集: {dataset} ===")
        
        dataset_path = Path(dataset)
        if not dataset_path.exists():
            print(f"❌ 数据集路径不存在: {dataset}")
            continue
        
        # 检查TrainDataset
        train_images = dataset_path / 'TrainDataset' / 'images'
        train_masks = dataset_path / 'TrainDataset' / 'masks'
        
        if train_images.exists() and train_masks.exists():
            # 获取图片文件
            image_files = [f for f in train_images.iterdir() 
                          if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png']]
            
            print(f"✅ 找到 {len(image_files)} 张训练图片")
            
            # 检查前5张图片的标签匹配
            matched = 0
            for i, img_file in enumerate(image_files[:5]):
                img_stem = img_file.stem
                # 查找对应的标签文件
                for ext in ['.png', '.jpg', '.jpeg']:
                    mask_file = train_masks / (img_stem + ext)
                    if mask_file.exists():
                        matched += 1
                        print(f"  ✅ {img_file.name} -> {mask_file.name}")
                        break
                else:
                    print(f"  ❌ {img_file.name} -> 未找到对应标签")
            
            print(f"标签匹配率: {matched}/5")
        else:
            print(f"❌ 训练数据目录不完整")

if __name__ == "__main__":
    test_dataset_structure()
