#!/usr/bin/env python3
"""
生成论文中的数据集示例图片 - 网格格式
类似您提供的示例图片，每个数据集5张图片，原图和标签并排显示
"""

import random
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path
import argparse

def get_image_files(dataset_path, subset='TrainDataset'):
    """获取指定数据集子集中的所有图片文件"""
    images_dir = Path(dataset_path) / subset / 'images'
    if not images_dir.exists():
        print(f"警告: 路径不存在 {images_dir}")
        return []
    
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_files = []
    
    for file_path in images_dir.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            image_files.append(file_path.name)
    
    return image_files

def find_mask_file(image_file, masks_dir):
    """查找对应的标签文件"""
    image_stem = Path(image_file).stem
    mask_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif']
    
    for ext in mask_extensions:
        mask_file = masks_dir / (image_stem + ext)
        if mask_file.exists():
            return mask_file
    return None

def resize_image(image, target_size=(200, 200)):
    """调整图片大小，保持宽高比"""
    return image.resize(target_size, Image.Resampling.LANCZOS)

def collect_dataset_samples(datasets, num_samples=5, subset='TrainDataset'):
    """收集所有数据集的样本"""
    all_samples = []
    
    for dataset_name, dataset_path in datasets.items():
        print(f"\n处理数据集: {dataset_name}")
        
        if not Path(dataset_path).exists():
            print(f"跳过不存在的数据集: {dataset_path}")
            continue
        
        # 获取图片文件
        image_files = get_image_files(dataset_path, subset)
        if not image_files:
            print(f"在 {dataset_path}/{subset}/images 中没有找到图片文件")
            continue
        
        print(f"找到 {len(image_files)} 张图片")
        
        # 随机选择图片
        num_to_select = min(num_samples, len(image_files))
        selected_images = random.sample(image_files, num_to_select)
        print(f"随机选择了 {num_to_select} 张图片")
        
        # 处理选中的图片
        images_dir = Path(dataset_path) / subset / 'images'
        masks_dir = Path(dataset_path) / subset / 'masks'
        
        for img_file in selected_images:
            image_path = images_dir / img_file
            mask_path = find_mask_file(img_file, masks_dir)
            
            if mask_path:
                try:
                    # 加载并调整图片大小
                    original = Image.open(image_path).convert('RGB')
                    mask = Image.open(mask_path).convert('L')
                    
                    # 调整为统一大小
                    target_size = (200, 200)
                    original_resized = resize_image(original, target_size)
                    mask_resized = resize_image(mask, target_size)
                    
                    all_samples.append({
                        'original': original_resized,
                        'mask': mask_resized,
                        'dataset': dataset_name,
                        'filename': img_file
                    })
                    print(f"  ✅ 处理完成: {img_file}")
                    
                except Exception as e:
                    print(f"  ❌ 处理失败 {img_file}: {e}")
            else:
                print(f"  ❌ 找不到标签: {img_file}")
    
    return all_samples

def create_grid_figure(samples, output_path):
    """创建网格布局的图片"""
    if not samples:
        print("没有样本可处理")
        return False
    
    # 按数据集分组
    dataset_groups = {}
    for sample in samples:
        dataset = sample['dataset']
        if dataset not in dataset_groups:
            dataset_groups[dataset] = []
        dataset_groups[dataset].append(sample)
    
    # 计算布局
    sample_size = 200
    gap = 15
    label_height = 25
    dataset_title_height = 40
    
    # 每行显示5个样本对（原图+标签）
    samples_per_row = 5
    pair_width = sample_size * 2 + gap  # 原图+间隔+标签
    row_width = pair_width * samples_per_row + gap * (samples_per_row - 1)
    
    # 计算总高度
    total_height = 60  # 顶部标题
    for dataset_name, dataset_samples in dataset_groups.items():
        rows_needed = (len(dataset_samples) + samples_per_row - 1) // samples_per_row
        dataset_height = (dataset_title_height + 
                         (sample_size + label_height) * rows_needed + 
                         gap * (rows_needed - 1) + 
                         30)  # 数据集间距
        total_height += dataset_height
    
    # 创建画布
    canvas = Image.new('RGB', (row_width, total_height), 'white')
    draw = ImageDraw.Draw(canvas)
    
    # 设置字体
    try:
        title_font = ImageFont.truetype("arial.ttf", 24)
        dataset_font = ImageFont.truetype("arial.ttf", 18)
        label_font = ImageFont.truetype("arial.ttf", 12)
    except:
        try:
            title_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 24)
            dataset_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 18)
            label_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 12)
        except:
            title_font = ImageFont.load_default()
            dataset_font = ImageFont.load_default()
            label_font = ImageFont.load_default()
    
    # 绘制主标题
    main_title = "Dataset Examples"
    title_bbox = draw.textbbox((0, 0), main_title, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    draw.text(((row_width - title_width) // 2, 20), main_title, fill='black', font=title_font)
    
    # 绘制各数据集
    current_y = 60
    
    for dataset_name, dataset_samples in dataset_groups.items():
        # 绘制数据集标题
        dataset_title = f"{dataset_name} Dataset"
        title_bbox = draw.textbbox((0, 0), dataset_title, font=dataset_font)
        title_width = title_bbox[2] - title_bbox[0]
        draw.text(((row_width - title_width) // 2, current_y), dataset_title, fill='black', font=dataset_font)
        current_y += dataset_title_height
        
        # 绘制样本
        for i, sample in enumerate(dataset_samples):
            row = i // samples_per_row
            col = i % samples_per_row
            
            # 计算位置
            x = col * (pair_width + gap)
            y = current_y + row * (sample_size + label_height + gap)
            
            # 放置原图
            canvas.paste(sample['original'], (x, y))
            
            # 放置标签图
            mask_x = x + sample_size + gap
            canvas.paste(sample['mask'].convert('RGB'), (mask_x, y))
            
            # 添加标签文字
            text_y = y + sample_size + 5
            draw.text((x + sample_size//2 - 20, text_y), "Original", fill='black', font=label_font)
            draw.text((mask_x + sample_size//2 - 30, text_y), "Ground Truth", fill='black', font=label_font)
        
        # 更新Y位置
        rows_used = (len(dataset_samples) + samples_per_row - 1) // samples_per_row
        current_y += (sample_size + label_height) * rows_used + gap * (rows_used - 1) + 30
    
    # 保存图片
    canvas.save(output_path, 'PNG', quality=95)
    print(f"\n✅ 图片已保存: {output_path}")
    print(f"图片尺寸: {canvas.width} x {canvas.height}")
    print(f"包含 {len(samples)} 个样本，来自 {len(dataset_groups)} 个数据集")
    
    return True

def main():
    parser = argparse.ArgumentParser(description='生成论文数据集示例网格图片')
    parser.add_argument('--output', '-o', default='dataset_grid.png', 
                       help='输出图片文件名 (默认: dataset_grid.png)')
    parser.add_argument('--num_samples', '-n', type=int, default=5,
                       help='每个数据集选择的样本数量 (默认: 5)')
    parser.add_argument('--seed', '-s', type=int, default=42,
                       help='随机种子 (默认: 42)')
    parser.add_argument('--subset', default='TrainDataset',
                       choices=['TrainDataset', 'TestDataset', 'ValidaDataset'],
                       help='使用的数据集子集 (默认: TrainDataset)')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(args.seed)
    
    # 定义数据集
    datasets = {
        'DeepCrack': 'DeepCrack',
        'GAPS384': 'GAPS384', 
        'CrackLS315': 'CrackLS315'
    }
    
    print(f"开始生成论文数据集网格图片...")
    print(f"输出文件: {args.output}")
    print(f"每个数据集样本数: {args.num_samples}")
    print(f"使用子集: {args.subset}")
    print(f"随机种子: {args.seed}")
    print("-" * 50)
    
    # 收集样本
    samples = collect_dataset_samples(datasets, args.num_samples, args.subset)
    
    if not samples:
        print("❌ 没有收集到任何样本")
        return
    
    # 创建网格图片
    success = create_grid_figure(samples, args.output)
    
    if success:
        print(f"\n🎉 成功生成论文网格图片!")
        print(f"文件位置: {Path(args.output).absolute()}")
    else:
        print(f"\n❌ 生成图片失败")

if __name__ == "__main__":
    main()
