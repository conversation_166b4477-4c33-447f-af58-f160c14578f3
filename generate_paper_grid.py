#!/usr/bin/env python3
"""
生成论文中的数据集示例图片 - 网格格式
类似您提供的示例图片，每个数据集5张图片，原图和标签并排显示
"""

import random
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path
import argparse

def get_image_files(dataset_path, subset='TrainDataset'):
    """获取指定数据集子集中的所有图片文件"""
    images_dir = Path(dataset_path) / subset / 'images'
    if not images_dir.exists():
        print(f"警告: 路径不存在 {images_dir}")
        return []
    
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_files = []
    
    for file_path in images_dir.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            image_files.append(file_path.name)
    
    return image_files

def find_mask_file(image_file, masks_dir):
    """查找对应的标签文件"""
    image_stem = Path(image_file).stem
    mask_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif']
    
    for ext in mask_extensions:
        mask_file = masks_dir / (image_stem + ext)
        if mask_file.exists():
            return mask_file
    return None

def resize_image(image, target_size=(200, 200)):
    """调整图片大小，保持宽高比"""
    return image.resize(target_size, Image.Resampling.LANCZOS)

def collect_dataset_samples(datasets, num_samples=5, subset='TrainDataset'):
    """收集所有数据集的样本"""
    all_samples = []
    
    for dataset_name, dataset_path in datasets.items():
        print(f"\n处理数据集: {dataset_name}")
        
        if not Path(dataset_path).exists():
            print(f"跳过不存在的数据集: {dataset_path}")
            continue
        
        # 获取图片文件
        image_files = get_image_files(dataset_path, subset)
        if not image_files:
            print(f"在 {dataset_path}/{subset}/images 中没有找到图片文件")
            continue
        
        print(f"找到 {len(image_files)} 张图片")
        
        # 随机选择图片
        num_to_select = min(num_samples, len(image_files))
        selected_images = random.sample(image_files, num_to_select)
        print(f"随机选择了 {num_to_select} 张图片")
        
        # 处理选中的图片
        images_dir = Path(dataset_path) / subset / 'images'
        masks_dir = Path(dataset_path) / subset / 'masks'
        
        for img_file in selected_images:
            image_path = images_dir / img_file
            mask_path = find_mask_file(img_file, masks_dir)
            
            if mask_path:
                try:
                    # 加载并调整图片大小
                    original = Image.open(image_path).convert('RGB')
                    mask = Image.open(mask_path).convert('L')
                    
                    # 调整为统一大小（长方形）
                    target_size = (240, 160)  # 宽x高，长方形
                    original_resized = resize_image(original, target_size)
                    mask_resized = resize_image(mask, target_size)
                    
                    all_samples.append({
                        'original': original_resized,
                        'mask': mask_resized,
                        'dataset': dataset_name,
                        'filename': img_file
                    })
                    print(f"  ✅ 处理完成: {img_file}")
                    
                except Exception as e:
                    print(f"  ❌ 处理失败 {img_file}: {e}")
            else:
                print(f"  ❌ 找不到标签: {img_file}")
    
    return all_samples

def create_grid_figure(samples, output_path):
    """创建网格布局的图片"""
    if not samples:
        print("没有样本可处理")
        return False

    # 按数据集分组
    dataset_groups = {}
    for sample in samples:
        dataset = sample['dataset']
        if dataset not in dataset_groups:
            dataset_groups[dataset] = []
        dataset_groups[dataset].append(sample)

    # 计算布局
    sample_width = 240   # 图片宽度（长方形）
    sample_height = 160  # 图片高度（长方形）
    col_gap = 1  # 列间距
    row_gap = 1  # 行间距（进一步减小）
    dataset_name_width = 100  # 数据集名称区域宽度

    # 每行显示5个样本，两行布局：第一行原图，第二行标签
    samples_per_row = 5
    content_width = sample_width * samples_per_row + col_gap * (samples_per_row - 1)
    total_width = dataset_name_width + col_gap + content_width

    # 计算总高度
    total_height = 1  # 顶部边距
    dataset_gap =1  # 数据集之间的间距（减小）
    for dataset_name, dataset_samples in dataset_groups.items():
        # 每个数据集需要2行：第一行原图，第二行标签
        dataset_height = sample_height * 2 + row_gap + dataset_gap  # 两行图片 + 行间距 + 数据集间距
        total_height += dataset_height

    # 创建画布
    canvas = Image.new('RGB', (total_width, total_height), 'white')
    draw = ImageDraw.Draw(canvas)

    # 设置字体 - 使用Times New Roman，字体更大
    try:
        dataset_font = ImageFont.truetype("times.ttf", 24)
    except:
        try:
            dataset_font = ImageFont.truetype("C:/Windows/Fonts/times.ttf", 24)
        except:
            try:
                dataset_font = ImageFont.truetype("C:/Windows/Fonts/timesbd.ttf", 24)
            except:
                try:
                    dataset_font = ImageFont.truetype("arial.ttf", 24)
                except:
                    dataset_font = ImageFont.load_default()

    # 绘制各数据集
    current_y = 20

    for dataset_name, dataset_samples in dataset_groups.items():
        # 计算数据集区域的中心Y位置（两行布局）
        dataset_content_height = sample_height * 2 + row_gap  # 两行图片 + 行间距
        dataset_center_y = current_y + dataset_content_height // 2

        # 在左侧绘制数据集名称（纵向书写，文字旋转90度）
        # 创建一个临时图片来绘制旋转的文字
        text_bbox = draw.textbbox((0, 0), dataset_name, font=dataset_font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]

        # 创建临时画布绘制文字
        temp_img = Image.new('RGBA', (text_width + 10, text_height + 10), (255, 255, 255, 0))
        temp_draw = ImageDraw.Draw(temp_img)
        temp_draw.text((5, 5), dataset_name, fill='black', font=dataset_font)

        # 旋转文字90度（逆时针）
        rotated_text = temp_img.rotate(90, expand=True)

        # 计算放置位置（垂直居中）
        rotated_width = rotated_text.width
        rotated_height = rotated_text.height
        text_x = (dataset_name_width - rotated_width) // 2
        text_y = dataset_center_y - rotated_height // 2

        # 将旋转的文字粘贴到主画布上
        canvas.paste(rotated_text, (text_x, text_y), rotated_text)

        # 绘制样本 - 两行布局
        content_start_x = dataset_name_width + col_gap

        # 只取前5个样本
        samples_to_show = dataset_samples[:samples_per_row]

        for i, sample in enumerate(samples_to_show):
            # 计算列位置
            x = content_start_x + i * (sample_width + col_gap)

            # 第一行：原图
            y_original = current_y
            canvas.paste(sample['original'], (x, y_original))

            # 第二行：标签图
            y_mask = current_y + sample_height + row_gap
            canvas.paste(sample['mask'].convert('RGB'), (x, y_mask))

        # 更新Y位置
        current_y += dataset_content_height + dataset_gap  # 数据集间距（减小）

    # 保存图片
    canvas.save(output_path, 'PNG', quality=95)
    print(f"\n✅ 图片已保存: {output_path}")
    print(f"图片尺寸: {canvas.width} x {canvas.height}")
    print(f"包含 {len(samples)} 个样本，来自 {len(dataset_groups)} 个数据集")

    return True

def main():
    parser = argparse.ArgumentParser(description='生成论文数据集示例网格图片')
    parser.add_argument('--output', '-o', default='dataset_grid.png', 
                       help='输出图片文件名 (默认: dataset_grid.png)')
    parser.add_argument('--num_samples', '-n', type=int, default=5,
                       help='每个数据集选择的样本数量 (默认: 5)')
    parser.add_argument('--seed', '-s', type=int, default=42,
                       help='随机种子 (默认: 42)')
    parser.add_argument('--subset', default='TrainDataset',
                       choices=['TrainDataset', 'TestDataset', 'ValidaDataset'],
                       help='使用的数据集子集 (默认: TrainDataset)')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(args.seed)
    
    # 定义数据集
    datasets = {
        'DeepCrack': 'DeepCrack',
        'GAPS384': 'GAPS384', 
        'CrackLS315': 'CrackLS315'
    }
    
    print(f"开始生成论文数据集网格图片...")
    print(f"输出文件: {args.output}")
    print(f"每个数据集样本数: {args.num_samples}")
    print(f"使用子集: {args.subset}")
    print(f"随机种子: {args.seed}")
    print("-" * 50)
    
    # 收集样本
    samples = collect_dataset_samples(datasets, args.num_samples, args.subset)
    
    if not samples:
        print("❌ 没有收集到任何样本")
        return
    
    # 创建网格图片
    success = create_grid_figure(samples, args.output)
    
    if success:
        print(f"\n🎉 成功生成论文网格图片!")
        print(f"文件位置: {Path(args.output).absolute()}")
    else:
        print(f"\n❌ 生成图片失败")

if __name__ == "__main__":
    main()
